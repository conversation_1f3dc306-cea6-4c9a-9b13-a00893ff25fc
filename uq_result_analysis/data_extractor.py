#!/usr/bin/env python3
"""
UQ结果数据提取器
从MongoDB中提取UQ分析结果并保存为CSV文件，用于后续分析
"""

import pandas as pd
import numpy as np
from pymongo import MongoClient
import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import argparse

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UQDataExtractor:
    """UQ数据提取器"""
    
    def __init__(self, mongo_host='localhost', mongo_port=27017, db_name='LLM-UQ'):
        """初始化数据提取器"""
        self.client = MongoClient(mongo_host, mongo_port)
        self.db = self.client[db_name]
        self.output_dir = Path('uq_result_analysis/data')
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def get_available_collections(self) -> List[str]:
        """获取所有UQ结果collection"""
        collections = self.db.list_collection_names()
        uq_collections = [name for name in collections if 'UQ_result' in name]
        return uq_collections
    
    def extract_collection_data(self, collection_name: str) -> pd.DataFrame:
        """从指定collection提取数据"""
        logger.info(f"提取collection: {collection_name}")
        
        collection = self.db[collection_name]
        total_docs = collection.count_documents({})
        logger.info(f"总文档数: {total_docs}")
        
        # 提取所有文档
        docs = list(collection.find())
        
        # 转换为扁平化的数据结构
        rows = []
        for doc in docs:
            base_info = self._extract_base_info(doc)
            
            # 提取每个UQ方法的结果
            if 'uq_results' in doc:
                for method_name, method_result in doc['uq_results'].items():
                    row = base_info.copy()
                    row.update(self._extract_method_result(method_name, method_result))
                    rows.append(row)
        
        df = pd.DataFrame(rows)
        logger.info(f"提取到 {len(df)} 行数据")
        return df
    
    def _extract_base_info(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """提取基础信息"""
        base_info = {
            'document_id': str(doc['_id']),
            'llm_model': doc.get('llm_model', 'unknown'),
            'task_name': doc.get('group_key', {}).get('task_name', 'unknown'),
            'dataset_source': doc.get('group_key', {}).get('dataset_source', 'unknown'),
            'prompt_seed': doc.get('group_key', {}).get('prompt_seed', None),
            'input_text': doc.get('group_key', {}).get('input_text', '')[:200],  # 截断长文本
            'n_responses': doc.get('metadata', {}).get('n_responses', 0),
            'successful_methods': doc.get('metadata', {}).get('successful_methods', 0),
            'failed_methods': doc.get('metadata', {}).get('failed_methods', 0),
            'created_at': doc.get('timestamps', {}).get('created_at', None),
        }
        return base_info
    
    def _extract_method_result(self, method_name: str, method_result: Dict[str, Any]) -> Dict[str, Any]:
        """提取UQ方法结果"""
        result = {
            'uq_method': method_name,
            'uq_value': method_result.get('uq_value', None),
            'status': method_result.get('status', 'unknown'),
            'computed_at': method_result.get('computed_at', None),
        }
        
        # 提取额外的指标
        if 'full_result' in method_result:
            full_result = method_result['full_result']
            result.update({
                'uncertainty_score': full_result.get('uncertainty_score', None),
                'mean_similarity': full_result.get('mean_similarity', None),
                'num_responses': full_result.get('num_responses', None),
                'method_type': full_result.get('method', None),
            })
            
            # 提取特定方法的额外指标
            if 'entropy' in full_result:
                result['entropy'] = full_result['entropy']
            if 'variance' in full_result:
                result['variance'] = full_result['variance']
            if 'consistency' in full_result:
                result['consistency'] = full_result['consistency']
            if 'disagreement' in full_result:
                result['disagreement'] = full_result['disagreement']
        
        return result
    
    def extract_all_data(self) -> Dict[str, pd.DataFrame]:
        """提取指定collection的数据"""
        # 指定要导出的collections
        target_collections = [
            'UQ_result_counterfactual_qa',
            'UQ_result_explorative_coding',
            'UQ_result_sentiment_analysis',
            'UQ_result_topic_labeling'
        ]

        # 获取所有可用的collections并过滤
        available_collections = self.get_available_collections()
        collections_to_extract = [name for name in target_collections if name in available_collections]

        logger.info(f"目标collections: {target_collections}")
        logger.info(f"可用collections: {available_collections}")
        logger.info(f"将要提取的collections: {collections_to_extract}")

        # 检查是否有未找到的collections
        missing_collections = [name for name in target_collections if name not in available_collections]
        if missing_collections:
            logger.warning(f"未找到以下collections: {missing_collections}")

        all_data = {}
        for collection_name in collections_to_extract:
            try:
                df = self.extract_collection_data(collection_name)
                if not df.empty:
                    all_data[collection_name] = df

                    # 保存单独的CSV文件
                    output_file = self.output_dir / f"{collection_name}.csv"
                    df.to_csv(output_file, index=False)
                    logger.info(f"保存到: {output_file}")

            except Exception as e:
                logger.error(f"提取 {collection_name} 时出错: {e}")

        return all_data
    
    def create_combined_dataset(self, all_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """创建合并的数据集"""
        logger.info("创建合并数据集")
        
        combined_dfs = []
        for collection_name, df in all_data.items():
            df = df.copy()
            df['collection_source'] = collection_name
            combined_dfs.append(df)
        
        if combined_dfs:
            combined_df = pd.concat(combined_dfs, ignore_index=True)
            
            # 保存合并的数据集
            output_file = self.output_dir / "combined_uq_results.csv"
            combined_df.to_csv(output_file, index=False)
            logger.info(f"合并数据集保存到: {output_file}")
            
            return combined_df
        else:
            logger.warning("没有数据可合并")
            return pd.DataFrame()
    
    def generate_summary_stats(self, combined_df: pd.DataFrame) -> Dict[str, Any]:
        """生成数据摘要统计"""
        if combined_df.empty:
            return {}
        
        summary = {
            'total_records': len(combined_df),
            'unique_models': combined_df['llm_model'].nunique(),
            'unique_tasks': combined_df['task_name'].nunique(),
            'unique_methods': combined_df['uq_method'].nunique(),
            'models': combined_df['llm_model'].value_counts().to_dict(),
            'tasks': combined_df['task_name'].value_counts().to_dict(),
            'methods': combined_df['uq_method'].value_counts().to_dict(),
            'collections': combined_df['collection_source'].value_counts().to_dict(),
        }
        
        # UQ值的统计
        uq_values = combined_df['uq_value'].dropna()
        if not uq_values.empty:
            summary['uq_value_stats'] = {
                'count': len(uq_values),
                'mean': float(uq_values.mean()),
                'std': float(uq_values.std()),
                'min': float(uq_values.min()),
                'max': float(uq_values.max()),
                'median': float(uq_values.median()),
            }
        
        # 保存摘要
        summary_file = self.output_dir / "data_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"数据摘要保存到: {summary_file}")
        return summary

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='提取UQ结果数据')
    parser.add_argument('--mongo-host', default='localhost', help='MongoDB主机')
    parser.add_argument('--mongo-port', type=int, default=27017, help='MongoDB端口')
    parser.add_argument('--db-name', default='LLM-UQ', help='数据库名称')
    
    args = parser.parse_args()
    
    # 创建提取器
    extractor = UQDataExtractor(args.mongo_host, args.mongo_port, args.db_name)
    
    # 提取所有数据
    all_data = extractor.extract_all_data()
    
    # 创建合并数据集
    combined_df = extractor.create_combined_dataset(all_data)
    
    # 生成摘要统计
    summary = extractor.generate_summary_stats(combined_df)
    
    # 打印摘要
    print("\n=== 数据提取摘要 ===")
    print(f"总记录数: {summary.get('total_records', 0)}")
    print(f"模型数量: {summary.get('unique_models', 0)}")
    print(f"任务数量: {summary.get('unique_tasks', 0)}")
    print(f"UQ方法数量: {summary.get('unique_methods', 0)}")
    
    if 'uq_value_stats' in summary:
        stats = summary['uq_value_stats']
        print(f"\nUQ值统计:")
        print(f"  数量: {stats['count']}")
        print(f"  均值: {stats['mean']:.4f}")
        print(f"  标准差: {stats['std']:.4f}")
        print(f"  范围: [{stats['min']:.4f}, {stats['max']:.4f}]")

if __name__ == "__main__":
    main()
