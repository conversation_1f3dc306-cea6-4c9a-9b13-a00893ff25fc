#!/usr/bin/env python3
"""
基于已提取的 UQ 结果 CSV (无需重新编码原始 responses) 对 sentiment_analysis / topic_labeling 任务做如下分析：
1. 直接利用 Embedding 方法 (EmbeddingE5UQ / EmbeddingQwenUQ) 在 full_result 中的 avg_distance_to_reference (responses 与 reference 的均值距离) 与 avg_candidate_distance (responses 两两平均距离 / pairwise) 作为 X 与基础 Y。
2. 支持用多个指定 UQ 方法 (列表) 的 uq_score (uncertainty_score 或 uq_value) 作为多组 Y，与 X (reference 距离) 共同绘制散点并分别线性拟合。
3. 生成：
    - 主 CSV (聚合每个 document_id: reference_distance, pairwise_distance, 各 UQ 方法分数)
    - 单个或多方法散点图 (颜色区分方法) + 拟合直线
    - 拟合参数 JSON 摘要

数据来源: uq_result_analysis/data/UQ_result_{task}.csv (由 data_extractor 生成)。该 CSV 每行 = document_id + uq_method。需 pivot 成 document 粒度。

依赖: pandas, numpy, matplotlib, (可选) scikit-learn；若无则使用 numpy 线性回归。

示例:
python embedding_reference_scatter.py --task sentiment_analysis --uq-methods EmbeddingE5UQ,LUQUQ,NumSetsUQ
python embedding_reference_scatter.py --task topic_labeling --uq-methods EmbeddingQwenUQ,E5

注意: 若某 document 缺少 avg_distance_to_reference (reference 不存在或未设置) 将被过滤。

"""
from __future__ import annotations
import os, argparse, json, math
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import pandas as pd
import numpy as np
from tqdm import tqdm

try:
    from sklearn.linear_model import LinearRegression
    SKLEARN_AVAILABLE = True
except Exception:
    SKLEARN_AVAILABLE = False

# 项目内部依赖
 # 不再需要运行时重新编码 responses, 直接使用 CSV 中 embedding 方法保存的统计指标

DATA_DIR = Path('uq_result_analysis/uq_result_analysis/data')
FIG_DIR = Path('uq_result_analysis/figures')
FIG_DIR.mkdir(parents=True, exist_ok=True)

REFERENCE_TASKS = {"sentiment_analysis", "topic_labeling"}

def safe_float(v):
    try:
        if pd.isna(v):
            return np.nan
        return float(v)
    except Exception:
        return np.nan

def load_task_dataframe(task: str) -> pd.DataFrame:
    csv_path = DATA_DIR / f"UQ_result_{task}.csv"
    if not csv_path.exists():
        raise FileNotFoundError(f"未找到 {csv_path}, 请先运行 data_extractor.py 提取数据")
    df = pd.read_csv(csv_path)
    return df

def prepare_grouped(df: pd.DataFrame) -> pd.DataFrame:
    # 每个 document_id + uq_method 是一行; 需要聚合为每个 input_text 的多响应集合
    # 假设同一 document_id 代表一个 input_text + 多 responses
    # 选择我们需要的列
    return df

def extract_embedding_distances(df: pd.DataFrame) -> pd.DataFrame:
    """过滤 embedding 相关方法行，提取 reference & pairwise 距离。
    兼容字段: avg_distance_to_reference, avg_candidate_distance, uncertainty_score.
    返回: document_id, reference_distance, pairwise_distance
    如果 embedding 方法不止一个，优先 E5 其次 Qwen；缺失时尽量回退任意 embedding 行。
    """
    emb_df = df[df['uq_method'].str.contains('Embedding', case=False, na=False)].copy()
    if emb_df.empty:
        raise ValueError('CSV 中未找到 Embedding 方法行。')
    # 计算优先级
    def priority(m):
        m_lower = m.lower()
        if 'e5' in m_lower:
            return 0
        if 'qwen' in m_lower:
            return 1
        return 2
    emb_df['__prio'] = emb_df['uq_method'].map(priority)
    emb_df.sort_values(['document_id','__prio'], inplace=True)
    # 去重保留优先行
    keep = emb_df.drop_duplicates(subset=['document_id'], keep='first')
    keep['reference_distance'] = keep['avg_distance_to_reference'].apply(safe_float)
    # pairwise distance 优先 avg_candidate_distance => uncertainty_score => uq_value
    keep['pairwise_distance'] = keep['avg_candidate_distance'].apply(safe_float)
    mask_missing = keep['pairwise_distance'].isna()
    keep.loc[mask_missing, 'pairwise_distance'] = keep.loc[mask_missing, 'uncertainty_score'].apply(safe_float)
    mask_missing2 = keep['pairwise_distance'].isna()
    keep.loc[mask_missing2, 'pairwise_distance'] = keep.loc[mask_missing2, 'uq_value'].apply(safe_float)
    out = keep[['document_id','reference_distance','pairwise_distance']].copy()
    out = out.dropna(subset=['reference_distance','pairwise_distance'])
    return out

def extract_multiple_uq_scores(df: pd.DataFrame, methods: List[str]) -> pd.DataFrame:
    """Pivot 指定 uq_method 列表的分数 (uncertainty_score 或 uq_value)。"""
    subset = df[df['uq_method'].isin(methods)].copy()
    if subset.empty:
        return pd.DataFrame({'document_id':[]})
    # 选值列
    def row_score(r):
        for k in ['uncertainty_score','uq_value']:
            v = r.get(k)
            if not pd.isna(v):
                return v
        return np.nan
    subset['uq_score_extracted'] = subset.apply(row_score, axis=1)
    wide = subset.pivot_table(index='document_id', columns='uq_method', values='uq_score_extracted', aggfunc='first')
    wide.reset_index(inplace=True)
    return wide

 # 不再需要原始 responses 解码逻辑

def linear_fit(x: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
    mask = (~np.isnan(x)) & (~np.isnan(y))
    x2 = x[mask][:, None]
    y2 = y[mask]
    if len(x2) < 2:
        return {"coef": None, "intercept": None, "r2": None, "n": len(x2)}
    if SKLEARN_AVAILABLE:
        model = LinearRegression().fit(x2, y2)
        r2 = model.score(x2, y2)
        return {"coef": float(model.coef_[0]), "intercept": float(model.intercept_), "r2": float(r2), "n": int(len(x2))}
    # fallback
    x_mean, y_mean = x2.mean(), y2.mean()
    num = ((x2 - x_mean) * (y2 - y_mean)).sum()
    den = ((x2 - x_mean)**2).sum()
    if den == 0:
        return {"coef": None, "intercept": None, "r2": None, "n": len(x2)}
    a = num / den
    b = y_mean - a * x_mean
    # r2
    ss_tot = ((y2 - y_mean)**2).sum()
    ss_res = ((y2 - (a * x2 + b))**2).sum()
    r2 = 1 - ss_res/ss_tot if ss_tot > 0 else None
    return {"coef": float(a), "intercept": float(b), "r2": float(r2) if r2 is not None else None, "n": int(len(x2))}


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--task', required=True, choices=sorted(list(REFERENCE_TASKS)))
    parser.add_argument('--uq-methods', default='EmbeddingE5UQ', help='逗号分隔的多个 UQ 方法名称, 会分别与 reference 距离绘制')
    parser.add_argument('--limit', type=int, default=0, help='仅使用前N个document (按 CSV 出现顺序)')
    parser.add_argument('--include-pairwise', action='store_true', help='除指定 UQ 方法外, 也绘制 pairwise_distance 基础散点')
    args = parser.parse_args()

    df = load_task_dataframe(args.task)
    emb_base = extract_embedding_distances(df)
    if args.limit > 0:
        # 限制 document 数量
        keep_ids = emb_base['document_id'].head(args.limit).tolist()
        emb_base = emb_base[emb_base['document_id'].isin(keep_ids)]

    uq_methods = [m.strip() for m in args.uq_methods.split(',') if m.strip()]
    uq_wide = extract_multiple_uq_scores(df, uq_methods)
    merged = emb_base.merge(uq_wide, on='document_id', how='left')
    merged['task'] = args.task
    # 保存基础汇总 CSV
    base_csv = FIG_DIR / f'embedding_reference_multi_{args.task}.csv'
    merged.to_csv(base_csv, index=False)
    print(f'保存聚合数据到 {base_csv} (rows={len(merged)})')

    # 拟合并生成图
    fit_results = {}
    try:
        import matplotlib.pyplot as plt
        plt.figure(figsize=(7,5))
        x = merged['reference_distance'].values
        color_cycle = plt.rcParams['axes.prop_cycle'].by_key()['color']
        color_idx = 0
        # 可选 pairwise baseline
        if args.include_pairwise:
            y_pair = merged['pairwise_distance'].values
            fit_pair = linear_fit(x, y_pair)
            fit_results['pairwise_distance'] = fit_pair
            plt.scatter(merged['reference_distance'], merged['pairwise_distance'], s=14, alpha=0.45, label=f'pairwise (R2={fit_pair.get("r2"):.3f if fit_pair.get("r2") is not None else "-"})', color='grey')
            if fit_pair['coef'] is not None:
                xs = np.linspace(float(np.nanmin(x)), float(np.nanmax(x)), 120)
                ys = fit_pair['coef'] * xs + fit_pair['intercept']
                plt.plot(xs, ys, '--', color='grey')
        # 每个 UQ 方法
        for m in uq_methods:
            if m not in merged.columns:
                continue
            y = merged[m].values
            if np.all(np.isnan(y)):
                continue
            fit_m = linear_fit(x, y)
            fit_results[m] = fit_m
            c = color_cycle[color_idx % len(color_cycle)]
            color_idx += 1
            plt.scatter(merged['reference_distance'], y, s=16, alpha=0.65, label=f'{m} (R2={fit_m.get("r2"):.3f if fit_m.get("r2") is not None else "-"})', color=c)
            if fit_m['coef'] is not None:
                xs = np.linspace(float(np.nanmin(x)), float(np.nanmax(x)), 120)
                ys = fit_m['coef'] * xs + fit_m['intercept']
                plt.plot(xs, ys, '-', color=c, linewidth=1.2)
        plt.xlabel('Mean distance (responses vs reference)')
        plt.ylabel('UQ score / pairwise distance')
        title_suffix = f"methods={len(uq_methods)}"
        plt.title(f'{args.task} embedding-reference scatter ({title_suffix}, n={len(merged)})')
        plt.legend(fontsize=8, frameon=False, ncol=1)
        plt.tight_layout()
        png_file = FIG_DIR / f'embedding_reference_multi_{args.task}.png'
        plt.savefig(png_file, dpi=220)
        print(f'保存图像到 {png_file}')
    except Exception as e:
        print('绘图失败:', e)
    # 保存拟合结果 JSON
    fit_json = FIG_DIR / f'embedding_reference_multi_{args.task}_fits.json'
    with open(fit_json, 'w', encoding='utf-8') as f:
        json.dump(fit_results, f, ensure_ascii=False, indent=2)
    print(f'拟合参数保存到 {fit_json}')

if __name__ == '__main__':
    main()
